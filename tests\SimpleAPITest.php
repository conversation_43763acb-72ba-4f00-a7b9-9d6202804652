<?php

use fehrlich\ImmoScoutAPI\ImmoScoutAPI;
use fehrlich\ImmoScoutAPI\exceptions\AuthException;
use fehrlich\ImmoScoutAPI\exceptions\InvalidResponse;
use fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException;
use PHPUnit\Framework\TestCase;

/**
 * Simple test implementation of ImmoScoutAPI for testing purposes
 */
class SimpleTestAPI extends ImmoScoutAPI
{
    // Storage for tokens during tests
    private static $requestToken = null;
    private static $requestSecret = null;
    private static $accessToken = null;
    private static $accessSecret = null;

    public function saveRequestToken(string $token, string $secret): void
    {
        self::$requestToken = $token;
        self::$requestSecret = $secret;
    }
    
    public function restoreRequestToken(): array
    {
        return [
            self::$requestToken ?? "test_request_token",
            self::$requestSecret ?? "test_request_secret",
        ];
    }

    public function saveAccessToken(string $token, string $secret): void
    {
        self::$accessToken = $token;
        self::$accessSecret = $secret;
    }
    
    public static function restoreAccessToken(): array
    {
        return [
            self::$accessToken ?? "test_access_token",
            self::$accessSecret ?? "test_access_secret",
        ];
    }
    
    // Helper methods for testing
    public static function resetTokens(): void
    {
        self::$requestToken = null;
        self::$requestSecret = null;
        self::$accessToken = null;
        self::$accessSecret = null;
    }
    
    public static function setAccessTokens(string $token, string $secret): void
    {
        self::$accessToken = $token;
        self::$accessSecret = $secret;
    }
}

/**
 * Simple but comprehensive test suite for ImmoScout API
 */
final class SimpleAPITest extends TestCase
{
    private SimpleTestAPI $client;

    protected function setUp(): void
    {
        parent::setUp();
        SimpleTestAPI::resetTokens();
        $this->client = SimpleTestAPI::createClient("test_consumer_key", "test_consumer_secret");
    }

    protected function tearDown(): void
    {
        SimpleTestAPI::resetTokens();
        parent::tearDown();
    }

    // ========================================
    // BASIC CONFIGURATION TESTS
    // ========================================

    public function testCreateClient(): void
    {
        $client = SimpleTestAPI::createClient("test_key", "test_secret");
        $this->assertInstanceOf(SimpleTestAPI::class, $client);
    }

    public function testCreateClientWithoutAuthorization(): void
    {
        $client = SimpleTestAPI::createClient("test_key", "test_secret", false);
        $this->assertInstanceOf(SimpleTestAPI::class, $client);
    }

    public function testDefaultDomain(): void
    {
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
    }

    public function testSandboxSwitching(): void
    {
        // Test default sandbox mode
        $this->client->useSandbox();
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
        
        // Test production mode
        $this->client->dontUseSandbox();
        $this->assertEquals("immobilienscout24.de", $this->client->getDomain());
        
        // Test switching back to sandbox
        $this->client->useSandbox();
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
    }

    public function testSetUser(): void
    {
        // Test that setUser method can be called without errors
        $this->client->setUser("custom_user");
        
        // Since we can't directly test the private property, we just verify
        // the method doesn't throw an exception
        $this->assertTrue(true);
    }

    // ========================================
    // AUTHENTICATION AND TOKEN TESTS
    // ========================================

    public function testTokenManagement(): void
    {
        // Test request token management
        $this->client->saveRequestToken("test_req_token", "test_req_secret");
        $requestToken = $this->client->restoreRequestToken();
        $this->assertEquals(["test_req_token", "test_req_secret"], $requestToken);

        // Test access token management
        $this->client->saveAccessToken("test_access_token", "test_access_secret");
        $accessToken = SimpleTestAPI::restoreAccessToken();
        $this->assertEquals(["test_access_token", "test_access_secret"], $accessToken);
    }

    public function testIsVerified(): void
    {
        // Test with valid tokens
        SimpleTestAPI::setAccessTokens("valid_token", "valid_secret");
        $this->assertTrue($this->client->isVerified());

        // Test with default tokens (should still be verified)
        SimpleTestAPI::resetTokens();
        $this->assertTrue($this->client->isVerified());
    }

    // ========================================
    // EXCEPTION TESTS
    // ========================================

    public function testExceptionClasses(): void
    {
        // Test that exception classes exist and can be instantiated
        $authException = new AuthException('Test auth error');
        $this->assertInstanceOf(\Exception::class, $authException);
        $this->assertEquals('Test auth error', $authException->getMessage());

        $invalidResponse = new InvalidResponse('Test invalid response', 400);
        $this->assertInstanceOf(\Exception::class, $invalidResponse);
        $this->assertEquals('Test invalid response', $invalidResponse->getMessage());
        $this->assertEquals(400, $invalidResponse->getCode());

        $tokenException = new InvalidTokenException('Test token error');
        $this->assertInstanceOf(\Exception::class, $tokenException);
        $this->assertEquals('Test token error', $tokenException->getMessage());
    }

    public function testInvalidResponseWithAdditionalData(): void
    {
        $response = new \stdClass();
        $messages = ['error' => 'test'];
        
        $exception = new InvalidResponse('Test message', 500, null, $response, $messages);
        
        $this->assertEquals($response, $exception->getResponse());
        $this->assertEquals($messages, $exception->getMessages());
    }

    // ========================================
    // TYPE SAFETY TESTS
    // ========================================

    public function testTypeSafety(): void
    {
        // Test that all methods accept the correct types
        $this->client->setUser("string_user");
        $this->client->useSandbox();
        $this->client->dontUseSandbox();
        
        // Test token methods with proper types
        $this->client->saveRequestToken("token", "secret");
        $this->client->saveAccessToken("access_token", "access_secret");
        
        $requestTokens = $this->client->restoreRequestToken();
        $this->assertIsArray($requestTokens);
        $this->assertCount(2, $requestTokens);
        
        $accessTokens = SimpleTestAPI::restoreAccessToken();
        $this->assertIsArray($accessTokens);
        $this->assertCount(2, $accessTokens);
    }

    // ========================================
    // PHP 8.3 COMPATIBILITY TESTS
    // ========================================

    public function testPHP83Compatibility(): void
    {
        // Test that the class works with PHP 8.3 features
        $this->assertInstanceOf(ImmoScoutAPI::class, $this->client);
        
        // Test type hints work correctly
        $this->client->setUser("test");
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
        
        // Test that dynamic properties attribute is working (no deprecation warnings)
        $this->assertTrue(true);
    }

    public function testMethodSignatures(): void
    {
        // Test that all methods have proper type hints and return types
        $reflection = new \ReflectionClass($this->client);
        
        // Check that createClient has proper return type
        $createClientMethod = $reflection->getMethod('createClient');
        $this->assertTrue($createClientMethod->hasReturnType());
        
        // Check that domain methods have proper return types
        $getDomainMethod = $reflection->getMethod('getDomain');
        $this->assertTrue($getDomainMethod->hasReturnType());
        $this->assertEquals('string', $getDomainMethod->getReturnType()->getName());
    }
}
