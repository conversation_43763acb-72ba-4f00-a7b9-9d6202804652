<?php

use fehrlich\ImmoScoutAPI\exceptions\AuthException;
use fehrlich\ImmoScoutAPI\exceptions\InvalidResponse;
use fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException;
use PHPUnit\Framework\TestCase;

/**
 * Basic tests that don't require client instantiation
 */
final class BasicTest extends TestCase
{
    // ========================================
    // EXCEPTION TESTS
    // ========================================

    public function testAuthException(): void
    {
        $exception = new AuthException('Test auth error');
        $this->assertInstanceOf(\Exception::class, $exception);
        $this->assertEquals('Test auth error', $exception->getMessage());
    }

    public function testInvalidResponseException(): void
    {
        $exception = new InvalidResponse('Test invalid response', 400);
        $this->assertInstanceOf(\Exception::class, $exception);
        $this->assertEquals('Test invalid response', $exception->getMessage());
        $this->assertEquals(400, $exception->getCode());
    }

    public function testInvalidTokenException(): void
    {
        $exception = new InvalidTokenException('Test token error');
        $this->assertInstanceOf(\Exception::class, $exception);
        $this->assertEquals('Test token error', $exception->getMessage());
    }

    public function testInvalidResponseWithAdditionalData(): void
    {
        $response = new \stdClass();
        $response->status = 'error';
        $messages = ['error' => 'test error'];
        
        $exception = new InvalidResponse('Test message', 500, null, $response, $messages);
        
        $this->assertEquals('Test message', $exception->getMessage());
        $this->assertEquals(500, $exception->getCode());
        $this->assertEquals($response, $exception->getResponse());
        $this->assertEquals($messages, $exception->getMessages());
    }

    // ========================================
    // CLASS EXISTENCE TESTS
    // ========================================

    public function testClassesExist(): void
    {
        $this->assertTrue(class_exists('fehrlich\ImmoScoutAPI\ImmoScoutAPI'));
        $this->assertTrue(class_exists('fehrlich\ImmoScoutAPI\exceptions\AuthException'));
        $this->assertTrue(class_exists('fehrlich\ImmoScoutAPI\exceptions\InvalidResponse'));
        $this->assertTrue(class_exists('fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException'));
    }

    // ========================================
    // PHP 8.3 COMPATIBILITY TESTS
    // ========================================

    public function testPHP83Features(): void
    {
        // Test that we're running on a compatible PHP version
        $this->assertTrue(version_compare(PHP_VERSION, '8.1.0', '>='));
        
        // Test that type declarations work
        $this->assertTrue(function_exists('json_encode'));
        $this->assertTrue(function_exists('json_decode'));
    }

    public function testTypeHints(): void
    {
        // Test that our exception classes have proper type hints
        $reflection = new \ReflectionClass(InvalidResponse::class);
        $constructor = $reflection->getConstructor();
        
        $this->assertNotNull($constructor);
        $parameters = $constructor->getParameters();
        
        // Check that the first parameter (message) has string type
        $this->assertTrue($parameters[0]->hasType());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
    }

    // ========================================
    // FUNCTIONALITY TESTS
    // ========================================

    public function testJsonHandling(): void
    {
        // Test that JSON encoding/decoding works as expected
        $data = ['test' => 'value', 'number' => 123];
        $json = json_encode($data);
        $decoded = json_decode($json, true);
        
        $this->assertEquals($data, $decoded);
    }

    public function testArrayHandling(): void
    {
        // Test array operations that the API uses
        $testArray = ['key1' => 'value1', 'key2' => 'value2'];
        
        $this->assertIsArray($testArray);
        $this->assertArrayHasKey('key1', $testArray);
        $this->assertEquals('value1', $testArray['key1']);
        $this->assertCount(2, $testArray);
    }

    public function testStringOperations(): void
    {
        // Test string operations used in the API
        $testString = 'sandbox-immobilienscout24.de';
        
        $this->assertIsString($testString);
        $this->assertStringContainsString('sandbox', $testString);
        $this->assertStringContainsString('immobilienscout24', $testString);
    }

    // ========================================
    // INTEGRATION READINESS TESTS
    // ========================================

    public function testRequiredExtensions(): void
    {
        // Test that required PHP extensions are available
        $this->assertTrue(extension_loaded('json'), 'JSON extension is required');
        $this->assertTrue(extension_loaded('curl'), 'cURL extension is required');
        $this->assertTrue(extension_loaded('openssl'), 'OpenSSL extension is required');
    }

    public function testComposerAutoload(): void
    {
        // Test that Composer autoloading is working
        $this->assertTrue(class_exists('GuzzleHttp\Client'));
        $this->assertTrue(class_exists('GuzzleHttp\Command\Guzzle\GuzzleClient'));
    }
}
