<?php

namespace fehrlich\ImmoScoutAPI\exceptions;

use Exception;
use Throwable;

class InvalidResponse extends Exception
{
    private mixed $response;
    private mixed $msgs;

    public function __construct(string $msg, int $code = 100, ?Throwable $prev = null, mixed $res = null, mixed $msgs = null)
    {
        parent::__construct($msg, $code, $prev);
        $this->msgs = $msgs;
        $this->response = $res;
    }

    public function getResponse(): mixed
    {
        return $this->response;
    }

    public function getMessages(): mixed
    {
        return $this->msgs;
    }
}
