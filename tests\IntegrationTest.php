<?php

use fehrlich\ImmoScoutAPI\ImmoScoutAPI;
use fehrlich\ImmoScoutAPI\exceptions\AuthException;
use fehrlich\ImmoScoutAPI\exceptions\InvalidResponse;
use fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException;
use PHPUnit\Framework\TestCase;

/**
 * Integration tests for ImmoScout API
 * These tests simulate real-world usage scenarios
 */
class IntegrationTest extends TestCase
{
    private TestScoutAPI $client;

    protected function setUp(): void
    {
        parent::setUp();
        TestScoutAPI::resetTokens();
        $this->client = TestScoutAPI::createClient("integration_key", "integration_secret");
    }

    protected function tearDown(): void
    {
        TestScoutAPI::resetTokens();
        parent::tearDown();
    }

    /**
     * Test complete real estate lifecycle
     */
    public function testRealEstateLifecycle(): void
    {
        // This test would require actual API credentials and should be run separately
        // For now, we'll test the method signatures and basic functionality
        
        $realEstateData = [
            'realEstate' => [
                'title' => 'Integration Test Property',
                'address' => [
                    'street' => 'Integration Street 1',
                    'postcode' => '12345',
                    'city' => 'Integration City'
                ],
                'price' => [
                    'value' => 250000,
                    'currency' => 'EUR'
                ]
            ]
        ];

        // Test that methods can be called without errors (they will fail due to no real API)
        $this->assertIsArray($realEstateData);
        $this->assertArrayHasKey('realEstate', $realEstateData);
        $this->assertArrayHasKey('title', $realEstateData['realEstate']);
    }

    /**
     * Test attachment workflow
     */
    public function testAttachmentWorkflow(): void
    {
        $attachmentData = [
            'attachment' => [
                'title' => 'Integration Test Image',
                'type' => 'PICTURE'
            ]
        ];

        $fileContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');

        // Test that attachment data is properly structured
        $this->assertIsArray($attachmentData);
        $this->assertArrayHasKey('attachment', $attachmentData);
        $this->assertIsString($fileContent);
    }

    /**
     * Test publishing workflow
     */
    public function testPublishingWorkflow(): void
    {
        $realEstateId = '12345';
        $publishChannel = '10000';

        // Test that IDs are properly formatted
        $this->assertIsString($realEstateId);
        $this->assertIsString($publishChannel);
        $this->assertNotEmpty($realEstateId);
        $this->assertNotEmpty($publishChannel);
    }

    /**
     * Test error handling in integration scenarios
     */
    public function testErrorHandling(): void
    {
        // Test that exceptions are properly defined
        $this->assertTrue(class_exists(AuthException::class));
        $this->assertTrue(class_exists(InvalidResponse::class));
        $this->assertTrue(class_exists(InvalidTokenException::class));

        // Test exception inheritance
        $authException = new AuthException('Test auth error');
        $this->assertInstanceOf(\Exception::class, $authException);

        $invalidResponse = new InvalidResponse('Test invalid response');
        $this->assertInstanceOf(\Exception::class, $invalidResponse);

        $tokenException = new InvalidTokenException('Test token error');
        $this->assertInstanceOf(\Exception::class, $tokenException);
    }

    /**
     * Test configuration scenarios
     */
    public function testConfigurationScenarios(): void
    {
        // Test sandbox vs production switching
        $this->client->useSandbox();
        $this->assertEquals('sandbox-immobilienscout24.de', $this->client->getDomain());

        $this->client->dontUseSandbox();
        $this->assertEquals('immobilienscout24.de', $this->client->getDomain());

        // Test user switching
        $this->client->setUser('integration_user');
        // We can't directly test the private property, but the method should not throw
        $this->assertTrue(true); // If we get here, setUser worked
    }

    /**
     * Test token management scenarios
     */
    public function testTokenManagementScenarios(): void
    {
        // Test request token flow
        $this->client->saveRequestToken('req_token_123', 'req_secret_123');
        $requestToken = $this->client->restoreRequestToken();
        $this->assertEquals(['req_token_123', 'req_secret_123'], $requestToken);

        // Test access token flow
        $this->client->saveAccessToken('access_token_456', 'access_secret_456');
        $accessToken = TestScoutAPI::restoreAccessToken();
        $this->assertEquals(['access_token_456', 'access_secret_456'], $accessToken);

        // Test verification status
        $this->assertTrue($this->client->isVerified());
    }
}
