<?php

/**
 * Simple test to verify PHP 8.3 compatibility
 */

require_once 'vendor/autoload.php';

use fehrlich\ImmoScoutAPI\ImmoScoutAPI;
use fehrlich\ImmoScoutAPI\exceptions\AuthException;
use fehrlich\ImmoScoutAPI\exceptions\InvalidResponse;
use fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException;

// Test class that extends the abstract ImmoScoutAPI
class TestAPI extends ImmoScoutAPI
{
    public function saveRequestToken(string $token, string $secret): void
    {
        // Mock implementation
        $_SESSION['test_request_token'] = $token;
        $_SESSION['test_request_secret'] = $secret;
    }

    public function restoreRequestToken(): array
    {
        // Mock implementation
        return [
            $_SESSION['test_request_token'] ?? 'test_token',
            $_SESSION['test_request_secret'] ?? 'test_secret'
        ];
    }

    public function saveAccessToken(string $token, string $secret): void
    {
        // Mock implementation
        $_SESSION['test_access_token'] = $token;
        $_SESSION['test_access_secret'] = $secret;
    }

    public static function restoreAccessToken(): array
    {
        // Mock implementation
        return [
            $_SESSION['test_access_token'] ?? 'test_access_token',
            $_SESSION['test_access_secret'] ?? 'test_access_secret'
        ];
    }
}

echo "PHP Version: " . PHP_VERSION . "\n";
echo "Testing PHP 8.3 compatibility...\n";

try {
    // Test basic instantiation and method calls
    echo "✓ Classes loaded successfully\n";
    
    // Test exception classes
    $invalidResponse = new InvalidResponse("Test message", 100);
    echo "✓ InvalidResponse exception works\n";
    
    $authException = new AuthException("Test auth error");
    echo "✓ AuthException works\n";
    
    $tokenException = new InvalidTokenException("Test token error");
    echo "✓ InvalidTokenException works\n";
    
    echo "✓ All PHP 8.3 compatibility tests passed!\n";
    
} catch (Throwable $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}

echo "\nPHP 8.3 compatibility verification completed successfully!\n";
