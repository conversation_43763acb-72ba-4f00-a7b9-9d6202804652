<?php

/**
 * Test runner script for ImmoScout API tests
 */

require_once 'vendor/autoload.php';

echo "=== ImmoScout API Test Suite ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Running comprehensive tests...\n\n";

// Check if PHPUnit is available
if (!class_exists('PHPUnit\Framework\TestCase')) {
    echo "❌ PHPUnit not found. Please install PHPUnit first.\n";
    echo "Run: composer install\n";
    exit(1);
}

// Basic syntax check
echo "🔍 Checking syntax...\n";
$files = [
    'src/ImmoScoutAPI.php',
    'src/exceptions/AuthException.php',
    'src/exceptions/InvalidResponse.php',
    'src/exceptions/InvalidTokenException.php',
    'tests/APITest.php',
    'tests/IntegrationTest.php'
];

foreach ($files as $file) {
    $output = [];
    $returnCode = 0;
    exec("php -l $file 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ $file - OK\n";
    } else {
        echo "❌ $file - Syntax Error:\n";
        echo implode("\n", $output) . "\n";
        exit(1);
    }
}

echo "\n🧪 Running unit tests...\n";

// Try to run PHPUnit tests
$testCommand = 'vendor/bin/phpunit tests/ --verbose';
if (PHP_OS_FAMILY === 'Windows') {
    $testCommand = 'vendor\\bin\\phpunit.bat tests\\ --verbose';
}

echo "Command: $testCommand\n";
passthru($testCommand, $exitCode);

if ($exitCode === 0) {
    echo "\n✅ All tests passed!\n";
} else {
    echo "\n❌ Some tests failed. Exit code: $exitCode\n";
}

echo "\n=== Test Summary ===\n";
echo "✅ Syntax checks: PASSED\n";
echo ($exitCode === 0 ? "✅" : "❌") . " Unit tests: " . ($exitCode === 0 ? "PASSED" : "FAILED") . "\n";
echo "\n🎉 ImmoScout API is ready for PHP 8.3!\n";

exit($exitCode);
