{"version": 1, "defects": {"ImmoScoutAPITest::testCreateClient": 4, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 4, "ImmoScoutAPITest::testDefaultDomain": 4, "ImmoScoutAPITest::testSandboxSwitching": 4, "ImmoScoutAPITest::testSetUser": 4, "ImmoScoutAPITest::testTokenManagement": 4, "ImmoScoutAPITest::testIsVerified": 4, "ImmoScoutAPITest::testCreateRealEstate": 4, "ImmoScoutAPITest::testUpdateRealEstate": 4, "ImmoScoutAPITest::testDeleteRealEstate": 4, "ImmoScoutAPITest::testCreateAttachment": 4, "ImmoScoutAPITest::testCreateSimpleAttachment": 4, "ImmoScoutAPITest::testUpdateAttachment": 4, "ImmoScoutAPITest::testDeleteAttachment": 4, "ImmoScoutAPITest::testUpdateAttachmentOrder": 4, "ImmoScoutAPITest::testPublish": 4, "ImmoScoutAPITest::testUnpublish": 4, "ImmoScoutAPITest::testGetPublishChannels": 4, "ImmoScoutAPITest::testGetContacts": 4, "ImmoScoutAPITest::testInvalidResponseException": 4, "ImmoScoutAPITest::testCreateRealEstateWithError": 4, "ImmoScoutAPITest::testAuthenticationError": 4, "ImmoScoutAPITest::testClientException": 4, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 4, "ImmoScoutAPITest::testPublishWithDefaultChannel": 4, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 4, "ImmoScoutAPITest::testEmptyResponseHandling": 4, "ImmoScoutAPITest::testInvalidJsonResponse": 4, "IntegrationTest::testRealEstateLifecycle": 4, "IntegrationTest::testAttachmentWorkflow": 4, "IntegrationTest::testPublishingWorkflow": 4, "IntegrationTest::testErrorHandling": 4, "IntegrationTest::testConfigurationScenarios": 4, "IntegrationTest::testTokenManagementScenarios": 4}, "times": {"ImmoScoutAPITest::testCreateClient": 0.05, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 0.004, "ImmoScoutAPITest::testDefaultDomain": 0.001, "ImmoScoutAPITest::testSandboxSwitching": 0.001, "ImmoScoutAPITest::testSetUser": 0, "ImmoScoutAPITest::testTokenManagement": 0.001, "ImmoScoutAPITest::testIsVerified": 0.001, "ImmoScoutAPITest::testCreateRealEstate": 0.001, "ImmoScoutAPITest::testUpdateRealEstate": 0.001, "ImmoScoutAPITest::testDeleteRealEstate": 0, "ImmoScoutAPITest::testCreateAttachment": 0, "ImmoScoutAPITest::testCreateSimpleAttachment": 0, "ImmoScoutAPITest::testUpdateAttachment": 0, "ImmoScoutAPITest::testDeleteAttachment": 0, "ImmoScoutAPITest::testUpdateAttachmentOrder": 0, "ImmoScoutAPITest::testPublish": 0, "ImmoScoutAPITest::testUnpublish": 0, "ImmoScoutAPITest::testGetPublishChannels": 0, "ImmoScoutAPITest::testGetContacts": 0, "ImmoScoutAPITest::testInvalidResponseException": 0.001, "ImmoScoutAPITest::testCreateRealEstateWithError": 0.001, "ImmoScoutAPITest::testAuthenticationError": 0.001, "ImmoScoutAPITest::testClientException": 0, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 0, "ImmoScoutAPITest::testPublishWithDefaultChannel": 0, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 0, "ImmoScoutAPITest::testEmptyResponseHandling": 0.001, "ImmoScoutAPITest::testInvalidJsonResponse": 0, "IntegrationTest::testRealEstateLifecycle": 0.001, "IntegrationTest::testAttachmentWorkflow": 0.001, "IntegrationTest::testPublishingWorkflow": 0.001, "IntegrationTest::testErrorHandling": 0.001, "IntegrationTest::testConfigurationScenarios": 0.001, "IntegrationTest::testTokenManagementScenarios": 0}}