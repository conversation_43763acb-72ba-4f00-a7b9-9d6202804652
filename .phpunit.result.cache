{"version": 1, "defects": {"ImmoScoutAPITest::testCreateClient": 4, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 4, "ImmoScoutAPITest::testDefaultDomain": 4, "ImmoScoutAPITest::testSandboxSwitching": 4, "ImmoScoutAPITest::testSetUser": 4, "ImmoScoutAPITest::testTokenManagement": 4, "ImmoScoutAPITest::testIsVerified": 4, "ImmoScoutAPITest::testCreateRealEstate": 4, "ImmoScoutAPITest::testUpdateRealEstate": 4, "ImmoScoutAPITest::testDeleteRealEstate": 4, "ImmoScoutAPITest::testCreateAttachment": 4, "ImmoScoutAPITest::testCreateSimpleAttachment": 4, "ImmoScoutAPITest::testUpdateAttachment": 4, "ImmoScoutAPITest::testDeleteAttachment": 4, "ImmoScoutAPITest::testUpdateAttachmentOrder": 4, "ImmoScoutAPITest::testPublish": 4, "ImmoScoutAPITest::testUnpublish": 4, "ImmoScoutAPITest::testGetPublishChannels": 4, "ImmoScoutAPITest::testGetContacts": 4, "ImmoScoutAPITest::testInvalidResponseException": 4, "ImmoScoutAPITest::testCreateRealEstateWithError": 4, "ImmoScoutAPITest::testAuthenticationError": 4, "ImmoScoutAPITest::testClientException": 4, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 4, "ImmoScoutAPITest::testPublishWithDefaultChannel": 4, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 4, "ImmoScoutAPITest::testEmptyResponseHandling": 4, "ImmoScoutAPITest::testInvalidJsonResponse": 4, "IntegrationTest::testRealEstateLifecycle": 4, "IntegrationTest::testAttachmentWorkflow": 4, "IntegrationTest::testPublishingWorkflow": 4, "IntegrationTest::testErrorHandling": 4, "IntegrationTest::testConfigurationScenarios": 4, "IntegrationTest::testTokenManagementScenarios": 4, "APITest::testCreateClient": 4, "APITest::testCreateClientWithoutAuthorization": 4, "APITest::testDefaultDomain": 4, "APITest::testSandboxSwitching": 4, "APITest::testSetUser": 4, "APITest::testTokenManagement": 4, "APITest::testIsVerified": 4, "APITest::testCreateRealEstate": 4, "APITest::testUpdateRealEstate": 4, "APITest::testDeleteRealEstate": 4, "APITest::testCreateAttachment": 4, "APITest::testCreateSimpleAttachment": 4, "APITest::testUpdateAttachment": 4, "APITest::testDeleteAttachment": 4, "APITest::testUpdateAttachmentOrder": 4, "APITest::testPublish": 4, "APITest::testUnpublish": 4, "APITest::testGetPublishChannels": 4, "APITest::testGetContacts": 4, "APITest::testInvalidResponseException": 4, "APITest::testCreateRealEstateWithError": 4, "APITest::testAuthenticationError": 4, "APITest::testClientException": 4, "APITest::testCreateAttachmentWithCustomMimeType": 4, "APITest::testPublishWithDefaultChannel": 4, "APITest::testUnpublishWithDefaultChannel": 4, "APITest::testEmptyResponseHandling": 4, "APITest::testInvalidJsonResponse": 4}, "times": {"ImmoScoutAPITest::testCreateClient": 0, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 0, "ImmoScoutAPITest::testDefaultDomain": 0, "ImmoScoutAPITest::testSandboxSwitching": 0, "ImmoScoutAPITest::testSetUser": 0.003, "ImmoScoutAPITest::testTokenManagement": 0, "ImmoScoutAPITest::testIsVerified": 0, "ImmoScoutAPITest::testCreateRealEstate": 0.001, "ImmoScoutAPITest::testUpdateRealEstate": 0, "ImmoScoutAPITest::testDeleteRealEstate": 0.002, "ImmoScoutAPITest::testCreateAttachment": 0, "ImmoScoutAPITest::testCreateSimpleAttachment": 0, "ImmoScoutAPITest::testUpdateAttachment": 0, "ImmoScoutAPITest::testDeleteAttachment": 0, "ImmoScoutAPITest::testUpdateAttachmentOrder": 0, "ImmoScoutAPITest::testPublish": 0, "ImmoScoutAPITest::testUnpublish": 0, "ImmoScoutAPITest::testGetPublishChannels": 0, "ImmoScoutAPITest::testGetContacts": 0, "ImmoScoutAPITest::testInvalidResponseException": 0, "ImmoScoutAPITest::testCreateRealEstateWithError": 0, "ImmoScoutAPITest::testAuthenticationError": 0, "ImmoScoutAPITest::testClientException": 0, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 0, "ImmoScoutAPITest::testPublishWithDefaultChannel": 0, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 0, "ImmoScoutAPITest::testEmptyResponseHandling": 0, "ImmoScoutAPITest::testInvalidJsonResponse": 0, "IntegrationTest::testRealEstateLifecycle": 0.001, "IntegrationTest::testAttachmentWorkflow": 0.001, "IntegrationTest::testPublishingWorkflow": 0.001, "IntegrationTest::testErrorHandling": 0.001, "IntegrationTest::testConfigurationScenarios": 0.001, "IntegrationTest::testTokenManagementScenarios": 0.001, "APITest::testCreateClient": 0, "APITest::testCreateClientWithoutAuthorization": 0, "APITest::testDefaultDomain": 0.004, "APITest::testSandboxSwitching": 0.002, "APITest::testSetUser": 0.001, "APITest::testTokenManagement": 0, "APITest::testIsVerified": 0.001, "APITest::testCreateRealEstate": 0, "APITest::testUpdateRealEstate": 0, "APITest::testDeleteRealEstate": 0, "APITest::testCreateAttachment": 0, "APITest::testCreateSimpleAttachment": 0, "APITest::testUpdateAttachment": 0, "APITest::testDeleteAttachment": 0, "APITest::testUpdateAttachmentOrder": 0, "APITest::testPublish": 0.001, "APITest::testUnpublish": 0.001, "APITest::testGetPublishChannels": 0, "APITest::testGetContacts": 0.001, "APITest::testInvalidResponseException": 0.001, "APITest::testCreateRealEstateWithError": 0.001, "APITest::testAuthenticationError": 0.001, "APITest::testClientException": 0.001, "APITest::testCreateAttachmentWithCustomMimeType": 0.001, "APITest::testPublishWithDefaultChannel": 0.001, "APITest::testUnpublishWithDefaultChannel": 0.001, "APITest::testEmptyResponseHandling": 0, "APITest::testInvalidJsonResponse": 0.001}}