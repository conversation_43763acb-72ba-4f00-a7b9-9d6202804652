{"version": 1, "defects": {"ImmoScoutAPITest::testCreateClient": 4, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 4, "ImmoScoutAPITest::testDefaultDomain": 4, "ImmoScoutAPITest::testSandboxSwitching": 4, "ImmoScoutAPITest::testSetUser": 4, "ImmoScoutAPITest::testTokenManagement": 4, "ImmoScoutAPITest::testIsVerified": 4, "ImmoScoutAPITest::testCreateRealEstate": 4, "ImmoScoutAPITest::testUpdateRealEstate": 4, "ImmoScoutAPITest::testDeleteRealEstate": 4, "ImmoScoutAPITest::testCreateAttachment": 4, "ImmoScoutAPITest::testCreateSimpleAttachment": 4, "ImmoScoutAPITest::testUpdateAttachment": 4, "ImmoScoutAPITest::testDeleteAttachment": 4, "ImmoScoutAPITest::testUpdateAttachmentOrder": 4, "ImmoScoutAPITest::testPublish": 4, "ImmoScoutAPITest::testUnpublish": 4, "ImmoScoutAPITest::testGetPublishChannels": 4, "ImmoScoutAPITest::testGetContacts": 4, "ImmoScoutAPITest::testInvalidResponseException": 4, "ImmoScoutAPITest::testCreateRealEstateWithError": 4, "ImmoScoutAPITest::testAuthenticationError": 4, "ImmoScoutAPITest::testClientException": 4, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 4, "ImmoScoutAPITest::testPublishWithDefaultChannel": 4, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 4, "ImmoScoutAPITest::testEmptyResponseHandling": 4, "ImmoScoutAPITest::testInvalidJsonResponse": 4, "IntegrationTest::testRealEstateLifecycle": 4, "IntegrationTest::testAttachmentWorkflow": 4, "IntegrationTest::testPublishingWorkflow": 4, "IntegrationTest::testErrorHandling": 4, "IntegrationTest::testConfigurationScenarios": 4, "IntegrationTest::testTokenManagementScenarios": 4, "APITest::testCreateClient": 4, "APITest::testCreateClientWithoutAuthorization": 4, "APITest::testDefaultDomain": 4, "APITest::testSandboxSwitching": 4, "APITest::testSetUser": 4, "APITest::testTokenManagement": 4, "APITest::testIsVerified": 4, "APITest::testCreateRealEstate": 4, "APITest::testUpdateRealEstate": 4, "APITest::testDeleteRealEstate": 4, "APITest::testCreateAttachment": 4, "APITest::testCreateSimpleAttachment": 4, "APITest::testUpdateAttachment": 4, "APITest::testDeleteAttachment": 4, "APITest::testUpdateAttachmentOrder": 4, "APITest::testPublish": 4, "APITest::testUnpublish": 4, "APITest::testGetPublishChannels": 4, "APITest::testGetContacts": 4, "APITest::testInvalidResponseException": 4, "APITest::testCreateRealEstateWithError": 4, "APITest::testAuthenticationError": 4, "APITest::testClientException": 4, "APITest::testCreateAttachmentWithCustomMimeType": 4, "APITest::testPublishWithDefaultChannel": 4, "APITest::testUnpublishWithDefaultChannel": 4, "APITest::testEmptyResponseHandling": 4, "APITest::testInvalidJsonResponse": 4, "SimpleAPITest::testCreateClient": 4, "SimpleAPITest::testCreateClientWithoutAuthorization": 4, "SimpleAPITest::testDefaultDomain": 4, "SimpleAPITest::testSandboxSwitching": 4, "SimpleAPITest::testSetUser": 4, "SimpleAPITest::testTokenManagement": 4, "SimpleAPITest::testIsVerified": 4, "SimpleAPITest::testExceptionClasses": 4, "SimpleAPITest::testInvalidResponseWithAdditionalData": 4, "SimpleAPITest::testTypeSafety": 4, "SimpleAPITest::testPHP83Compatibility": 4, "SimpleAPITest::testMethodSignatures": 4, "BasicTest::testAuthException": 4, "BasicTest::testInvalidResponseException": 4, "BasicTest::testInvalidTokenException": 4, "BasicTest::testInvalidResponseWithAdditionalData": 4, "BasicTest::testClassesExist": 4, "BasicTest::testPHP83Features": 4, "BasicTest::testTypeHints": 4, "BasicTest::testJsonHandling": 4, "BasicTest::testArrayHandling": 4, "BasicTest::testStringOperations": 4, "BasicTest::testRequiredExtensions": 4, "BasicTest::testComposerAutoload": 4}, "times": {"ImmoScoutAPITest::testCreateClient": 0, "ImmoScoutAPITest::testCreateClientWithoutAuthorization": 0, "ImmoScoutAPITest::testDefaultDomain": 0, "ImmoScoutAPITest::testSandboxSwitching": 0, "ImmoScoutAPITest::testSetUser": 0.003, "ImmoScoutAPITest::testTokenManagement": 0, "ImmoScoutAPITest::testIsVerified": 0, "ImmoScoutAPITest::testCreateRealEstate": 0.001, "ImmoScoutAPITest::testUpdateRealEstate": 0, "ImmoScoutAPITest::testDeleteRealEstate": 0.002, "ImmoScoutAPITest::testCreateAttachment": 0, "ImmoScoutAPITest::testCreateSimpleAttachment": 0, "ImmoScoutAPITest::testUpdateAttachment": 0, "ImmoScoutAPITest::testDeleteAttachment": 0, "ImmoScoutAPITest::testUpdateAttachmentOrder": 0, "ImmoScoutAPITest::testPublish": 0, "ImmoScoutAPITest::testUnpublish": 0, "ImmoScoutAPITest::testGetPublishChannels": 0, "ImmoScoutAPITest::testGetContacts": 0, "ImmoScoutAPITest::testInvalidResponseException": 0, "ImmoScoutAPITest::testCreateRealEstateWithError": 0, "ImmoScoutAPITest::testAuthenticationError": 0, "ImmoScoutAPITest::testClientException": 0, "ImmoScoutAPITest::testCreateAttachmentWithCustomMimeType": 0, "ImmoScoutAPITest::testPublishWithDefaultChannel": 0, "ImmoScoutAPITest::testUnpublishWithDefaultChannel": 0, "ImmoScoutAPITest::testEmptyResponseHandling": 0, "ImmoScoutAPITest::testInvalidJsonResponse": 0, "IntegrationTest::testRealEstateLifecycle": 0.001, "IntegrationTest::testAttachmentWorkflow": 0.001, "IntegrationTest::testPublishingWorkflow": 0.001, "IntegrationTest::testErrorHandling": 0.001, "IntegrationTest::testConfigurationScenarios": 0.001, "IntegrationTest::testTokenManagementScenarios": 0.001, "APITest::testCreateClient": 0.003, "APITest::testCreateClientWithoutAuthorization": 0.002, "APITest::testDefaultDomain": 0, "APITest::testSandboxSwitching": 0, "APITest::testSetUser": 0, "APITest::testTokenManagement": 0, "APITest::testIsVerified": 0, "APITest::testCreateRealEstate": 0.001, "APITest::testUpdateRealEstate": 0, "APITest::testDeleteRealEstate": 0, "APITest::testCreateAttachment": 0, "APITest::testCreateSimpleAttachment": 0, "APITest::testUpdateAttachment": 0, "APITest::testDeleteAttachment": 0, "APITest::testUpdateAttachmentOrder": 0, "APITest::testPublish": 0, "APITest::testUnpublish": 0, "APITest::testGetPublishChannels": 0, "APITest::testGetContacts": 0.001, "APITest::testInvalidResponseException": 0, "APITest::testCreateRealEstateWithError": 0, "APITest::testAuthenticationError": 0, "APITest::testClientException": 0, "APITest::testCreateAttachmentWithCustomMimeType": 0.001, "APITest::testPublishWithDefaultChannel": 0, "APITest::testUnpublishWithDefaultChannel": 0, "APITest::testEmptyResponseHandling": 0, "APITest::testInvalidJsonResponse": 0, "SimpleAPITest::testCreateClient": 0.003, "SimpleAPITest::testCreateClientWithoutAuthorization": 0.002, "SimpleAPITest::testDefaultDomain": 0.001, "SimpleAPITest::testSandboxSwitching": 0.001, "SimpleAPITest::testSetUser": 0, "SimpleAPITest::testTokenManagement": 0, "SimpleAPITest::testIsVerified": 0, "SimpleAPITest::testExceptionClasses": 0, "SimpleAPITest::testInvalidResponseWithAdditionalData": 0, "SimpleAPITest::testTypeSafety": 0.001, "SimpleAPITest::testPHP83Compatibility": 0.001, "SimpleAPITest::testMethodSignatures": 0, "BasicTest::testAuthException": 0.006, "BasicTest::testInvalidResponseException": 0.003, "BasicTest::testInvalidTokenException": 0.001, "BasicTest::testInvalidResponseWithAdditionalData": 0.001, "BasicTest::testClassesExist": 0.002, "BasicTest::testPHP83Features": 0.001, "BasicTest::testTypeHints": 0.001, "BasicTest::testJsonHandling": 0.001, "BasicTest::testArrayHandling": 0.001, "BasicTest::testStringOperations": 0.001, "BasicTest::testRequiredExtensions": 0.001, "BasicTest::testComposerAutoload": 0.002}}