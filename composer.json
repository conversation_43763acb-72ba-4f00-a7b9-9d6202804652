{"name": "fehrlich/immoscout24-api-php", "description": "PHP Client for interacting with the Immoscout24 REST API", "type": "library", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"guzzlehttp/guzzle-services": "^1.3", "guzzlehttp/oauth-subscriber": "^0.6.0", "guzzlehttp/guzzle": "^7.4"}, "license": "MIT", "autoload": {"psr-4": {"fehrlich\\ImmoScoutAPI\\": "src/"}}, "require-dev": {"phpunit/phpunit": "^9.5"}}