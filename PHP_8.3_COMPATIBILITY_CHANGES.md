# PHP 8.3 Compatibility Changes

This document outlines the changes made to make the immoscout24-api-php library compatible with PHP 8.3.

## Summary of Changes

### 1. Dynamic Properties Fix
- **Issue**: PHP 8.2+ deprecated dynamic properties creation
- **Solution**: Added `#[\AllowDynamicProperties]` attribute to the `ImmoScoutAPI` class
- **File**: `src/ImmoScoutAPI.php`
- **Line**: Added attribute before class declaration

### 2. Type Declarations Added
Enhanced type safety by adding proper type hints throughout the codebase:

#### Abstract Methods
- `saveRequestToken(string $token, string $secret): void`
- `restoreRequestToken(): array`
- `saveAccessToken(string $token, string $secret): void`
- `restoreAccessToken(): array`

#### Public Methods
- `useSandbox(): void`
- `dontUseSandbox(): void`
- `getDomain(): string`
- `setUser(string $user): void`
- `createRealEstate(array $obj): string`
- `updateRealEstate(string $id, array $obj): bool`
- `deleteAttachment(string $reId, string $attachmentId): bool`
- `createAttachment(string $objId, array $attachmentData, string $content, string $mimeType = 'image/jpeg', string $fileName = 'image.jpg'): string`
- `createSimpleAttachment(string $objId, array $attachmentData): string`
- `updateAttachment(string $objId, string $id, array $attachmentData): bool`
- `updateAttachmentOrder(string $objId, array $orderArray): bool`
- `getContacts(): array`
- `deleteRealestate(string $reId): bool`
- `publish(string $reId, string $publishchannel = '10000'): string`
- `unpublish(string $reId, string $publishchannel = '10000'): bool`

#### Private Methods
- `validateToken(array $tokenArray): void`
- `getValidatedRequestToken(): array`
- `getValidatedAccessToken(): array`
- `getBaseUrl(): string`

#### Static Methods
- `createClient(string $key, string $secret, bool $authorized = true, ?callable $commandToRequestTransformer = null, ?callable $responseToResultTransformer = null, ?HandlerStack $commandHandlerStack = null, array $config = []): static`

### 3. Exception Class Updates
- **File**: `src/exceptions/InvalidResponse.php`
- Added proper type hints for constructor parameters
- Used `mixed` type for flexible response and message data
- Added `Throwable` import for proper type hinting

### 4. Composer.json Updates
- Added explicit PHP version requirement: `"php": "^8.1 || ^8.2 || ^8.3"`
- Updated PHPUnit version range to support newer versions: `"phpunit/phpunit": "^9.5 || ^10.0 || ^11.0"`

### 5. String Casting Improvements
- Ensured proper string casting in JSON decode operations
- Changed `json_decode($res->getBody(), true)` to `json_decode((string) $res->getBody(), true)`

## Backward Compatibility

All changes maintain backward compatibility with PHP 8.1 and 8.2:
- The `#[\AllowDynamicProperties]` attribute is ignored in PHP versions prior to 8.0
- Type hints are compatible with PHP 8.1+
- Nullable type syntax (`?Type`) is supported since PHP 7.1
- Union types (`Type1|Type2`) are supported since PHP 8.0

## Testing

A compatibility test file (`test_php83_compatibility.php`) was created to verify:
- Class loading and instantiation
- Exception handling
- Basic method calls
- Type compatibility

The test successfully runs on PHP 8.4.6, confirming compatibility with PHP 8.3.

## Dependencies

The current dependencies are compatible with PHP 8.3:
- `guzzlehttp/guzzle`: ^7.4 (supports PHP 8.3)
- `guzzlehttp/guzzle-services`: ^1.3 (supports PHP 8.3)
- `guzzlehttp/oauth-subscriber`: ^0.6.0 (compatible with PHP 8.3)

## Benefits

1. **Future-proof**: Code is ready for PHP 8.3 and later versions
2. **Better IDE support**: Type hints improve autocompletion and error detection
3. **Runtime safety**: Type declarations catch type-related errors earlier
4. **Performance**: Type hints can provide minor performance improvements
5. **Code quality**: More explicit and self-documenting code

## Migration Notes

For users upgrading to PHP 8.3:
1. Update your composer.json to require this updated version
2. Run `composer update` to get the latest compatible dependencies
3. No code changes required in your implementation classes
4. All existing functionality remains the same
