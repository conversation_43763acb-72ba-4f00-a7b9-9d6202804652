<?php

use fehrlich\ImmoScoutAPI\ImmoScoutAPI;
use fehrlich\ImmoScoutAPI\exceptions\AuthException;
use fehrlich\ImmoScoutAPI\exceptions\InvalidResponse;
use fehrlich\ImmoScoutAPI\exceptions\InvalidTokenException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use PHPUnit\Framework\TestCase;

/**
 * Test implementation of ImmoScoutAPI for testing purposes
 */
class TestScoutAPI extends ImmoScoutAPI
{
    public $token;
    public $secret;
    protected static $stack = null;

    // Storage for tokens during tests
    private static $requestToken = null;
    private static $requestSecret = null;
    private static $accessToken = null;
    private static $accessSecret = null;

    public static function setStack($stack)
    {
        TestScoutAPI::$stack = $stack;
    }

    public function saveRequestToken(string $token, string $secret): void
    {
        self::$requestToken = $token;
        self::$requestSecret = $secret;
    }

    public function restoreRequestToken(): array
    {
        return [
            self::$requestToken ?? "test_request_token",
            self::$requestSecret ?? "test_request_secret",
        ];
    }

    public function saveAccessToken(string $token, string $secret): void
    {
        self::$accessToken = $token;
        self::$accessSecret = $secret;
    }

    public static function restoreAccessToken(): array
    {
        return [
            self::$accessToken ?? "test_access_token",
            self::$accessSecret ?? "test_access_secret",
        ];
    }

    // Helper methods for testing
    public static function resetTokens(): void
    {
        self::$requestToken = null;
        self::$requestSecret = null;
        self::$accessToken = null;
        self::$accessSecret = null;
    }

    public static function setAccessTokens(string $token, string $secret): void
    {
        self::$accessToken = $token;
        self::$accessSecret = $secret;
    }
}

/**
 * Comprehensive test suite for ImmoScout API
 */
final class APITest extends TestCase
{
    private TestScoutAPI $client;
    private MockHandler $mockHandler;
    private array $historyContainer;

    protected function setUp(): void
    {
        parent::setUp();
        TestScoutAPI::resetTokens();

        // Create mock responses for different scenarios
        $this->mockHandler = new MockHandler();
        $this->historyContainer = [];

        $handlerStack = HandlerStack::create($this->mockHandler);
        $history = Middleware::history($this->historyContainer);
        $handlerStack->push($history);

        $this->client = TestScoutAPI::createClient("test_consumer_key", "test_consumer_secret");

        // Use reflection to set the private client property for testing
        $reflection = new \ReflectionClass($this->client);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->client, new Client([
            'base_uri' => "http://localhost",
            'handler' => $handlerStack,
            'verify' => false,
        ]));
    }

    protected function tearDown(): void
    {
        TestScoutAPI::resetTokens();
        parent::tearDown();
    }

    /**
     * Helper method to add mock responses
     */
    private function addMockResponse(int $statusCode, array $headers = [], string $body = ''): void
    {
        $this->mockHandler->append(new Response($statusCode, $headers, $body));
    }

    /**
     * Helper method to add mock exception
     */
    private function addMockException(\Throwable $exception): void
    {
        $this->mockHandler->append($exception);
    }

    /**
     * Helper method to get the last request made
     */
    private function getLastRequest(): ?Request
    {
        return end($this->historyContainer)['request'] ?? null;
    }

    // ========================================
    // BASIC CONFIGURATION TESTS
    // ========================================

    public function testCreateClient(): void
    {
        $client = TestScoutAPI::createClient("test_key", "test_secret");
        $this->assertInstanceOf(TestScoutAPI::class, $client);
    }

    public function testCreateClientWithoutAuthorization(): void
    {
        $client = TestScoutAPI::createClient("test_key", "test_secret", false);
        $this->assertInstanceOf(TestScoutAPI::class, $client);
    }

    public function testDefaultDomain(): void
    {
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
    }

    public function testSandboxSwitching(): void
    {
        // Test default sandbox mode
        $this->client->useSandbox();
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());

        // Test production mode
        $this->client->dontUseSandbox();
        $this->assertEquals("immobilienscout24.de", $this->client->getDomain());

        // Test switching back to sandbox
        $this->client->useSandbox();
        $this->assertEquals("sandbox-immobilienscout24.de", $this->client->getDomain());
    }

    public function testSetUser(): void
    {
        $this->client->setUser("custom_user");

        // We can't directly test the private $user property, but we can test
        // that it affects URL generation by making a call and checking the URL
        $this->addMockResponse(200, [], '{"test": "response"}');

        try {
            $this->client->getPublishChannels();
        } catch (\Exception $e) {
            // Expected to fail due to mock, but we can check the request URL
        }

        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertStringContainsString('custom_user', (string) $request->getUri());
    }

    // ========================================
    // AUTHENTICATION AND TOKEN TESTS
    // ========================================

    public function testTokenManagement(): void
    {
        // Test request token management
        $this->client->saveRequestToken("test_req_token", "test_req_secret");
        $requestToken = $this->client->restoreRequestToken();
        $this->assertEquals(["test_req_token", "test_req_secret"], $requestToken);

        // Test access token management
        $this->client->saveAccessToken("test_access_token", "test_access_secret");
        $accessToken = TestScoutAPI::restoreAccessToken();
        $this->assertEquals(["test_access_token", "test_access_secret"], $accessToken);
    }

    public function testIsVerified(): void
    {
        // Test with valid tokens
        TestScoutAPI::setAccessTokens("valid_token", "valid_secret");
        $this->assertTrue($this->client->isVerified());

        // Test with invalid tokens (empty array should cause InvalidTokenException)
        TestScoutAPI::resetTokens();

        // Create a test client with empty tokens to test invalid state
        $client = TestScoutAPI::createClient("key", "secret");

        // Override the static method temporarily by creating a subclass
        $invalidClient = new class("key", "secret") extends TestScoutAPI {
            public function __construct(private string $key, private string $secret) {}

            public static function restoreAccessToken(): array
            {
                return []; // Invalid token array
            }

            public function testIsVerified(): bool
            {
                try {
                    $this->getValidatedAccessToken();
                    return true;
                } catch (InvalidTokenException $ex) {
                    return false;
                }
            }
        };

        $this->assertFalse($invalidClient->testIsVerified());
    }

    // ========================================
    // REAL ESTATE MANAGEMENT TESTS
    // ========================================

    public function testCreateRealEstate(): void
    {
        $realEstateData = [
            'realEstate' => [
                'title' => 'Test Property',
                'address' => [
                    'street' => 'Test Street 1',
                    'postcode' => '12345',
                    'city' => 'Test City'
                ]
            ]
        ];

        // Mock successful creation response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [12345] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->createRealEstate($realEstateData);
        $this->assertEquals('12345', $result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('POST', $request->getMethod());
        $this->assertStringContainsString('realestate', (string) $request->getUri());
    }

    public function testUpdateRealEstate(): void
    {
        $realEstateData = [
            'realEstate' => [
                'title' => 'Updated Test Property',
                'address' => [
                    'street' => 'Updated Street 1',
                    'postcode' => '54321',
                    'city' => 'Updated City'
                ]
            ]
        ];

        // Mock successful update response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_UPDATED',
                        'message' => 'Resource updated successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->updateRealEstate('12345', $realEstateData);
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('PUT', $request->getMethod());
        $this->assertStringContainsString('realestate/12345', (string) $request->getUri());
    }

    public function testDeleteRealEstate(): void
    {
        // Mock successful deletion response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_DELETED',
                        'message' => 'Resource deleted successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->deleteRealestate('12345');
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('DELETE', $request->getMethod());
        $this->assertStringContainsString('realestate/12345', (string) $request->getUri());
    }

    // ========================================
    // ATTACHMENT MANAGEMENT TESTS
    // ========================================

    public function testCreateAttachment(): void
    {
        $attachmentData = [
            'attachment' => [
                'title' => 'Test Image',
                'type' => 'PICTURE'
            ]
        ];
        $fileContent = 'fake_image_content';

        // Mock successful creation response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [67890] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->createAttachment('12345', $attachmentData, $fileContent);
        $this->assertEquals('67890', $result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('POST', $request->getMethod());
        $this->assertStringContainsString('realestate/12345/attachment', (string) $request->getUri());
    }

    public function testCreateSimpleAttachment(): void
    {
        $attachmentData = [
            'attachment' => [
                'title' => 'Test Document',
                'type' => 'DOCUMENT'
            ]
        ];

        // Mock successful creation response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [67891] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->createSimpleAttachment('12345', $attachmentData);
        $this->assertEquals('67891', $result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('POST', $request->getMethod());
        $this->assertStringContainsString('realestate/12345/attachment', (string) $request->getUri());
    }

    public function testUpdateAttachment(): void
    {
        $attachmentData = [
            'attachment' => [
                'title' => 'Updated Test Image',
                'type' => 'PICTURE'
            ]
        ];

        // Mock successful update response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_UPDATED',
                        'message' => 'Resource updated successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->updateAttachment('12345', '67890', $attachmentData);
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('PUT', $request->getMethod());
        $this->assertStringContainsString('realestate/12345/attachment/67890', (string) $request->getUri());
    }

    public function testDeleteAttachment(): void
    {
        // Mock successful deletion response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_DELETED',
                        'message' => 'Resource deleted successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->deleteAttachment('12345', '67890');
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('DELETE', $request->getMethod());
        $this->assertStringContainsString('realestate/12345/attachment/67890', (string) $request->getUri());
    }

    public function testUpdateAttachmentOrder(): void
    {
        $orderArray = ['67890', '67891', '67892'];

        // Mock successful update response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_UPDATED',
                        'message' => 'Resource updated successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->updateAttachmentOrder('12345', $orderArray);
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('PUT', $request->getMethod());
        $this->assertStringContainsString('attachmentsorder', (string) $request->getUri());
    }

    // ========================================
    // PUBLISHING TESTS
    // ========================================

    public function testPublish(): void
    {
        // Mock successful publish response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [publish_123] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->publish('12345', '10000');
        $this->assertEquals('publish_123', $result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('POST', $request->getMethod());
        $this->assertStringContainsString('publish', (string) $request->getUri());
    }

    public function testUnpublish(): void
    {
        // Mock successful unpublish response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_DELETED',
                        'message' => 'Resource deleted successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->unpublish('12345', '10000');
        $this->assertTrue($result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('DELETE', $request->getMethod());
        $this->assertStringContainsString('publish/12345_10000', (string) $request->getUri());
    }

    public function testGetPublishChannels(): void
    {
        // Mock response with publish channels
        $responseBody = json_encode([
            'publishChannels' => [
                ['id' => '10000', 'name' => 'ImmobilienScout24'],
                ['id' => '10001', 'name' => 'Partner Portal']
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->getPublishChannels();
        $this->assertInstanceOf(\Psr\Http\Message\ResponseInterface::class, $result);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('GET', $request->getMethod());
        $this->assertStringContainsString('publishchannel', (string) $request->getUri());
    }

    // ========================================
    // CONTACT MANAGEMENT TESTS
    // ========================================

    public function testGetContacts(): void
    {
        // Mock response with contacts
        $responseBody = json_encode([
            'common.realtorContactDetailsList' => [
                'realtorContactDetails' => [
                    [
                        'id' => 'contact_1',
                        'firstname' => 'John',
                        'lastname' => 'Doe',
                        'email' => '<EMAIL>'
                    ],
                    [
                        'id' => 'contact_2',
                        'firstname' => 'Jane',
                        'lastname' => 'Smith',
                        'email' => '<EMAIL>'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->getContacts();
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals('John', $result[0]['firstname']);
        $this->assertEquals('Jane', $result[1]['firstname']);

        // Verify the request was made correctly
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertEquals('GET', $request->getMethod());
        $this->assertStringContainsString('contact', (string) $request->getUri());
    }

    // ========================================
    // ERROR HANDLING AND EXCEPTION TESTS
    // ========================================

    public function testInvalidResponseException(): void
    {
        // Test with malformed contacts response
        $responseBody = json_encode([
            'invalid' => 'response'
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        $this->expectException(InvalidResponse::class);
        $this->expectExceptionMessage('Response doesnt have the expected format');

        $this->client->getContacts();
    }

    public function testCreateRealEstateWithError(): void
    {
        $realEstateData = ['invalid' => 'data'];

        // Mock error response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'ERROR_RESOURCE_VALIDATION',
                        'message' => 'MESSAGE: Invalid data provided :'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(400, ['Content-Type' => 'application/json'], $responseBody);

        $this->expectException(InvalidResponse::class);
        $this->expectExceptionMessage('Did not get expected response');

        $this->client->createRealEstate($realEstateData);
    }

    public function testAuthenticationError(): void
    {
        // Mock 401 authentication error
        $responseBody = json_encode([
            'error' => 'Unauthorized'
        ]);

        $this->addMockResponse(401, ['Content-Type' => 'application/json'], $responseBody);

        $this->expectException(AuthException::class);
        $this->expectExceptionMessage('Immoscout24-API: Unauthorized');

        $this->client->getContacts();
    }

    public function testClientException(): void
    {
        // Mock a client exception (e.g., 404 Not Found)
        $exception = new ClientException(
            'Not Found',
            new Request('GET', 'test'),
            new Response(404, [], 'Not Found')
        );

        $this->addMockException($exception);

        // The method should handle the exception and return the response
        $result = $this->client->getPublishChannels();
        $this->assertInstanceOf(\Psr\Http\Message\ResponseInterface::class, $result);
        $this->assertEquals(404, $result->getStatusCode());
    }

    // ========================================
    // EDGE CASES AND INTEGRATION TESTS
    // ========================================

    public function testCreateAttachmentWithCustomMimeType(): void
    {
        $attachmentData = [
            'attachment' => [
                'title' => 'Test PDF',
                'type' => 'DOCUMENT'
            ]
        ];
        $fileContent = 'fake_pdf_content';

        // Mock successful creation response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [67892] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        $result = $this->client->createAttachment(
            '12345',
            $attachmentData,
            $fileContent,
            'application/pdf',
            'document.pdf'
        );
        $this->assertEquals('67892', $result);
    }

    public function testPublishWithDefaultChannel(): void
    {
        // Mock successful publish response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_CREATED',
                        'message' => 'Resource created with id [publish_default] successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(201, ['Content-Type' => 'application/json'], $responseBody);

        // Test with default channel (should be '10000')
        $result = $this->client->publish('12345');
        $this->assertEquals('publish_default', $result);

        // Verify the request contains the default channel
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $requestBody = (string) $request->getBody();
        $this->assertStringContainsString('10000', $requestBody);
    }

    public function testUnpublishWithDefaultChannel(): void
    {
        // Mock successful unpublish response
        $responseBody = json_encode([
            'common.messages' => [
                [
                    'message' => [
                        'messageCode' => 'MESSAGE_RESOURCE_DELETED',
                        'message' => 'Resource deleted successfully.'
                    ]
                ]
            ]
        ]);

        $this->addMockResponse(200, ['Content-Type' => 'application/json'], $responseBody);

        // Test with default channel (should be '10000')
        $result = $this->client->unpublish('12345');
        $this->assertTrue($result);

        // Verify the request URL contains the default channel
        $request = $this->getLastRequest();
        $this->assertNotNull($request);
        $this->assertStringContainsString('12345_10000', (string) $request->getUri());
    }

    public function testEmptyResponseHandling(): void
    {
        // Mock empty response
        $this->addMockResponse(200, ['Content-Type' => 'application/json'], '');

        // This should not throw an exception but handle empty response gracefully
        $result = $this->client->getPublishChannels();
        $this->assertInstanceOf(\Psr\Http\Message\ResponseInterface::class, $result);
    }

    public function testInvalidJsonResponse(): void
    {
        // Mock invalid JSON response
        $this->addMockResponse(200, ['Content-Type' => 'application/json'], 'invalid json {');

        $this->expectException(InvalidResponse::class);
        $this->expectExceptionMessage('invalid json response');

        // This should trigger the parseGetResponse method which handles invalid JSON
        $this->client->getContacts();
    }
}
