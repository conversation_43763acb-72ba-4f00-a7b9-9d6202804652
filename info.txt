phpinfo()
PHP Version => 8.4.6

System => Windows NT DESKTOP-HOME 10.0 build 26100 (Windows 11) AMD64
Build Date => Apr  9 2025 09:46:48
Build System => Microsoft Windows Server 2022 Datacenter [10.0.20348]
Compiler => Visual C++ 2022
Architecture => x64
Configure Command => cscript /nologo /e:jscript configure.js  "--enable-snapshot-build" "--enable-debug-pack" "--enable-object-out-dir=../obj/" "--enable-com-dotnet=shared" "--without-analyzer" "--with-pgo"
Server API => Command Line Interface
Virtual Directory Support => enabled
Configuration File (php.ini) Path =>  
Loaded Configuration File => (none)
Scan this dir for additional .ini files => C:\Users\<USER>\scoop\apps\php\current\cli;C:\Users\<USER>\scoop\apps\php\current\cli\conf.d;
Additional .ini files parsed => C:\Users\<USER>\scoop\apps\php\current\cli\php.ini,
C:\Users\<USER>\scoop\apps\php\current\cli\conf.d\xdebug.ini

PHP API => 20240924
PHP Extension => 20240924
Zend Extension => 420240924
Zend Extension Build => API420240924,TS,VS17
PHP Extension Build => API20240924,TS,VS17
PHP Integer Size => 64 bits
Debug Build => no
Thread Safety => enabled
Thread API => Windows Threads
Zend Signal Handling => disabled
Zend Memory Manager => enabled
Zend Multibyte Support => disabled
Zend Max Execution Timers => disabled
IPv6 Support => enabled
DTrace Support => disabled

Registered PHP Streams => compress.zlib, php, file, glob, data, http, ftp, https, ftps, phar
Registered Stream Socket Transports => tcp, udp, ssl, tls, tlsv1.0, tlsv1.1, tlsv1.2, tlsv1.3
Registered Stream Filters => convert.iconv.*, zlib.*, string.rot13, string.toupper, string.tolower, convert.*, consumed, dechunk

This program makes use of the Zend Scripting Language Engine:
Zend Engine v4.4.6, Copyright (c) Zend Technologies
    with Xdebug v3.4.0, Copyright (c) 2002-2024, by Derick Rethans


 _______________________________________________________________________


Configuration

bcmath

BCMath support => enabled

Directive => Local Value => Master Value
bcmath.scale => 0 => 0

calendar

Calendar support => enabled

Core

PHP Version => 8.4.6

Directive => Local Value => Master Value
allow_url_fopen => On => On
allow_url_include => Off => Off
arg_separator.input => & => &
arg_separator.output => & => &
auto_append_file => no value => no value
auto_globals_jit => On => On
auto_prepend_file => no value => no value
browscap => no value => no value
default_charset => UTF-8 => UTF-8
default_mimetype => text/html => text/html
disable_classes => no value => no value
disable_functions => no value => no value
display_errors => Off => Off
display_startup_errors => Off => Off
doc_root => no value => no value
docref_ext => no value => no value
docref_root => no value => no value
enable_dl => Off => Off
enable_post_data_reading => On => On
error_append_string => no value => no value
error_log => no value => no value
error_log_mode => 0644 => 0644
error_prepend_string => no value => no value
error_reporting => 22527 => 22527
expose_php => On => On
extension_dir => ext => ext
fiber.stack_size => no value => no value
file_uploads => On => On
hard_timeout => 2 => 2
highlight.comment => <span style="color: #FF8000">#FF8000</span> => <span style="color: #FF8000">#FF8000</span>
highlight.default => <span style="color: #0000BB">#0000BB</span> => <span style="color: #0000BB">#0000BB</span>
highlight.html => <span style="color: #000000">#000000</span> => <span style="color: #000000">#000000</span>
highlight.keyword => <span style="color: #007700">#007700</span> => <span style="color: #007700">#007700</span>
highlight.string => <span style="color: #DD0000">#DD0000</span> => <span style="color: #DD0000">#DD0000</span>
html_errors => Off => Off
ignore_repeated_errors => Off => Off
ignore_repeated_source => Off => Off
ignore_user_abort => Off => Off
implicit_flush => On => On
include_path => .;C:\php\pear => .;C:\php\pear
input_encoding => no value => no value
internal_encoding => no value => no value
log_errors => On => On
mail.add_x_header => Off => Off
mail.force_extra_parameters => no value => no value
mail.log => no value => no value
mail.mixed_lf_and_crlf => Off => Off
max_execution_time => 0 => 0
max_file_uploads => 20 => 20
max_input_nesting_level => 64 => 64
max_input_time => -1 => -1
max_input_vars => 1000 => 1000
max_multipart_body_parts => -1 => -1
memory_limit => 128M => 128M
open_basedir => no value => no value
output_buffering => 0 => 0
output_encoding => no value => no value
output_handler => no value => no value
post_max_size => 8M => 8M
precision => 14 => 14
realpath_cache_size => 4096K => 4096K
realpath_cache_ttl => 120 => 120
register_argc_argv => On => On
report_memleaks => On => On
report_zend_debug => Off => Off
request_order => GP => GP
sendmail_from => no value => no value
sendmail_path => no value => no value
serialize_precision => -1 => -1
short_open_tag => Off => Off
SMTP => localhost => localhost
smtp_port => 25 => 25
sys_temp_dir => no value => no value
syslog.facility => LOG_USER => LOG_USER
syslog.filter => no-ctrl => no-ctrl
syslog.ident => php => php
unserialize_callback_func => no value => no value
upload_max_filesize => 2M => 2M
upload_tmp_dir => no value => no value
user_dir => no value => no value
user_ini.cache_ttl => 300 => 300
user_ini.filename => .user.ini => .user.ini
variables_order => GPCS => GPCS
windows.show_crt_warning => Off => Off
xmlrpc_error_number => 0 => 0
xmlrpc_errors => Off => Off
zend.assertions => -1 => -1
zend.detect_unicode => On => On
zend.enable_gc => On => On
zend.exception_ignore_args => On => On
zend.exception_string_param_max_len => 0 => 0
zend.max_allowed_stack_size => 0 => 0
zend.multibyte => Off => Off
zend.reserved_stack_size => 0 => 0
zend.script_encoding => no value => no value

ctype

ctype functions => enabled

date

date/time support => enabled
timelib version => 2022.12
"Olson" Timezone Database Version => 2025.2
Timezone Database => internal
Default timezone => UTC

Directive => Local Value => Master Value
date.default_latitude => 31.7667 => 31.7667
date.default_longitude => 35.2333 => 35.2333
date.sunrise_zenith => 90.833333 => 90.833333
date.sunset_zenith => 90.833333 => 90.833333
date.timezone => UTC => UTC

dom

DOM/XML => enabled
DOM/XML API Version => 20031129
libxml Version => 2.11.9
HTML Support => enabled
XPath Support => enabled
XPointer Support => enabled
Schema Support => enabled
RelaxNG Support => enabled

filter

Input Validation and Filtering => enabled

Directive => Local Value => Master Value
filter.default => unsafe_raw => unsafe_raw
filter.default_flags => no value => no value

hash

hash support => enabled
Hashing Engines => md2 md4 md5 sha1 sha224 sha256 sha384 sha512/224 sha512/256 sha512 sha3-224 sha3-256 sha3-384 sha3-512 ripemd128 ripemd160 ripemd256 ripemd320 whirlpool tiger128,3 tiger160,3 tiger192,3 tiger128,4 tiger160,4 tiger192,4 snefru snefru256 gost gost-crypto adler32 crc32 crc32b crc32c fnv132 fnv1a32 fnv164 fnv1a64 joaat murmur3a murmur3c murmur3f xxh32 xxh64 xxh3 xxh128 haval128,3 haval160,3 haval192,3 haval224,3 haval256,3 haval128,4 haval160,4 haval192,4 haval224,4 haval256,4 haval128,5 haval160,5 haval192,5 haval224,5 haval256,5 

MHASH support => Enabled
MHASH API Version => Emulated Support

iconv

iconv support => enabled
iconv implementation => "libiconv"
iconv library version => 1.17

Directive => Local Value => Master Value
iconv.input_encoding => no value => no value
iconv.internal_encoding => no value => no value
iconv.output_encoding => no value => no value

json

json support => enabled

libxml

libXML support => active
libXML Compiled Version => 2.11.9
libXML Loaded Version => 21109
libXML streams => enabled

mysqlnd

mysqlnd => enabled
Version => mysqlnd 8.4.6
Compression => supported
core SSL => supported
extended SSL => supported
Command buffer size => 4096
Read buffer size => 32768
Read timeout => 86400
Collecting statistics => Yes
Collecting memory statistics => No
Tracing => n/a
Loaded plugins => mysqlnd,debug_trace,auth_plugin_mysql_native_password,auth_plugin_mysql_clear_password,auth_plugin_caching_sha2_password,auth_plugin_sha256_password
API Extensions =>  

openssl

OpenSSL support => enabled
OpenSSL Library Version => OpenSSL 3.0.16 11 Feb 2025
OpenSSL Header Version => OpenSSL 3.0.16 11 Feb 2025
Openssl default config => C:\Program Files\Common Files\SSL/openssl.cnf

Directive => Local Value => Master Value
openssl.cafile => no value => no value
openssl.capath => no value => no value

pcre

PCRE (Perl Compatible Regular Expressions) Support => enabled
PCRE Library Version => 10.44 2024-06-07
PCRE Unicode Version => 15.0.0
PCRE JIT Support => enabled
PCRE JIT Target => x86 64bit (little endian + unaligned)

Directive => Local Value => Master Value
pcre.backtrack_limit => 1000000 => 1000000
pcre.jit => On => On
pcre.recursion_limit => 100000 => 100000

PDO

PDO support => enabled
PDO drivers =>  

Phar

Phar: PHP Archive support => enabled
Phar API version => 1.1.1
Phar-based phar archives => enabled
Tar-based phar archives => enabled
ZIP-based phar archives => enabled
gzip compression => enabled
bzip2 compression => disabled (install ext/bz2)
OpenSSL support => enabled


Phar based on pear/PHP_Archive, original concept by Davey Shafik.
Phar fully realized by Gregory Beaver and Marcus Boerger.
Portions of tar implementation Copyright (c) 2003-2009 Tim Kientzle.
Directive => Local Value => Master Value
phar.cache_list => no value => no value
phar.readonly => On => On
phar.require_hash => On => On

random

Version => 8.4.6

readline

Readline Support => enabled
Readline library => WinEditLine

Directive => Local Value => Master Value
cli.pager => no value => no value
cli.prompt => \b \>  => \b \> 

Reflection

Reflection => enabled

session

Session Support => enabled
Registered save handlers => files user 
Registered serializer handlers => php_serialize php php_binary 

Directive => Local Value => Master Value
session.auto_start => Off => Off
session.cache_expire => 180 => 180
session.cache_limiter => nocache => nocache
session.cookie_domain => no value => no value
session.cookie_httponly => Off => Off
session.cookie_lifetime => 0 => 0
session.cookie_path => / => /
session.cookie_samesite => no value => no value
session.cookie_secure => Off => Off
session.gc_divisor => 1000 => 1000
session.gc_maxlifetime => 1440 => 1440
session.gc_probability => 1 => 1
session.lazy_write => On => On
session.name => PHPSESSID => PHPSESSID
session.referer_check => no value => no value
session.save_handler => files => files
session.save_path => no value => no value
session.serialize_handler => php => php
session.sid_bits_per_character => 4 => 4
session.sid_length => 32 => 32
session.upload_progress.cleanup => On => On
session.upload_progress.enabled => On => On
session.upload_progress.freq => 1% => 1%
session.upload_progress.min_freq => 1 => 1
session.upload_progress.name => PHP_SESSION_UPLOAD_PROGRESS => PHP_SESSION_UPLOAD_PROGRESS
session.upload_progress.prefix => upload_progress_ => upload_progress_
session.use_cookies => On => On
session.use_only_cookies => On => On
session.use_strict_mode => Off => Off
session.use_trans_sid => Off => Off

SimpleXML

SimpleXML support => enabled
Schema support => enabled

SPL

SPL support => enabled
Interfaces => OuterIterator, RecursiveIterator, SeekableIterator, SplObserver, SplSubject
Classes => AppendIterator, ArrayIterator, ArrayObject, BadFunctionCallException, BadMethodCallException, CachingIterator, CallbackFilterIterator, DirectoryIterator, DomainException, EmptyIterator, FilesystemIterator, FilterIterator, GlobIterator, InfiniteIterator, InvalidArgumentException, IteratorIterator, LengthException, LimitIterator, LogicException, MultipleIterator, NoRewindIterator, OutOfBoundsException, OutOfRangeException, OverflowException, ParentIterator, RangeException, RecursiveArrayIterator, RecursiveCachingIterator, RecursiveCallbackFilterIterator, RecursiveDirectoryIterator, RecursiveFilterIterator, RecursiveIteratorIterator, RecursiveRegexIterator, RecursiveTreeIterator, RegexIterator, RuntimeException, SplDoublyLinkedList, SplFileInfo, SplFileObject, SplFixedArray, SplHeap, SplMinHeap, SplMaxHeap, SplObjectStorage, SplPriorityQueue, SplQueue, SplStack, SplTempFileObject, UnderflowException, UnexpectedValueException

standard

Dynamic Library Support => enabled
Internal Sendmail Support for Windows => enabled

Directive => Local Value => Master Value
assert.active => On => On
assert.bail => Off => Off
assert.callback => no value => no value
assert.exception => On => On
assert.warning => On => On
auto_detect_line_endings => Off => Off
default_socket_timeout => 60 => 60
from => no value => no value
session.trans_sid_hosts => no value => no value
session.trans_sid_tags => a=href,area=href,frame=src,form= => a=href,area=href,frame=src,form=
unserialize_max_depth => 4096 => 4096
url_rewriter.hosts => no value => no value
url_rewriter.tags => form= => form=
user_agent => no value => no value

tokenizer

Tokenizer Support => enabled

xdebug

[1m__   __   _      _                 
[1m\ \ / /  | |    | |                
[1m \ V / __| | ___| |__  _   _  __ _ 
[1m  > < / _` |/ _ \ '_ \| | | |/ _` |
[1m / . \ (_| |  __/ |_) | |_| | (_| |
[1m/_/ \_\__,_|\___|_.__/ \__,_|\__, |
[1m                              __/ |
[1m                             |___/ 

[0mVersion => 3.4.0
Support Xdebug on Patreon, GitHub, or as a business: https://xdebug.org/support

             Enabled Features (through 'xdebug.mode' setting)             
Feature => Enabled/Disabled
Development Helpers => ✘ disabled
Coverage => ✘ disabled
GC Stats => ✘ disabled
Profiler => ✘ disabled
Step Debugger => ✔ enabled
Tracing => ✘ disabled

                            Optional Features                            
Compressed File Support => yes (gzip)
Clock Source => GetSystemTimePreciseAsFileTime
'xdebug://gateway' pseudo-host support => no
'xdebug://nameserver' pseudo-host support => no
Systemd Private Temp Directory => not enabled

Debugger => enabled
IDE Key =>  

Directive => Local Value => Master Value
xdebug.auto_trace => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.cli_color => 0 => 0
xdebug.client_discovery_header => HTTP_X_FORWARDED_FOR,REMOTE_ADDR => HTTP_X_FORWARDED_FOR,REMOTE_ADDR
xdebug.client_host => localhost => localhost
xdebug.client_port => 9003 => 9003
xdebug.cloud_id => no value => no value
xdebug.collect_assignments => Off => Off
xdebug.collect_includes => (setting removed in Xdebug 3) => (setting removed in Xdebug 3)
xdebug.collect_params => On => On
xdebug.collect_return => Off => Off
xdebug.collect_vars => (setting removed in Xdebug 3) => (setting removed in Xdebug 3)
xdebug.connect_timeout_ms => 200 => 200
xdebug.coverage_enable => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.default_enable => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.discover_client_host => On => On
xdebug.dump.COOKIE => no value => no value
xdebug.dump.ENV => no value => no value
xdebug.dump.FILES => no value => no value
xdebug.dump.GET => no value => no value
xdebug.dump.POST => no value => no value
xdebug.dump.REQUEST => no value => no value
xdebug.dump.SERVER => no value => no value
xdebug.dump.SESSION => no value => no value
xdebug.dump_globals => On => On
xdebug.dump_once => On => On
xdebug.dump_undefined => Off => Off
xdebug.file_link_format => no value => no value
xdebug.filename_format => no value => no value
xdebug.force_display_errors => Off => Off
xdebug.force_error_reporting => 0 => 0
xdebug.gc_stats_enable => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.gc_stats_output_dir => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.gc_stats_output_name => gcstats.%p => gcstats.%p
xdebug.halt_level => 0 => 0
xdebug.idekey => no value => no value
xdebug.log => no value => no value
xdebug.log_level => 7 => 7
xdebug.max_nesting_level => 512 => 512
xdebug.max_stack_frames => -1 => -1
xdebug.mode => debug => debug
xdebug.output_dir => C:\Windows\Temp => C:\Windows\Temp
xdebug.overload_var_dump => (setting removed in Xdebug 3) => (setting removed in Xdebug 3)
xdebug.profiler_append => Off => Off
xdebug.profiler_enable => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.profiler_enable_trigger => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.profiler_enable_trigger_value => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.profiler_output_dir => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.profiler_output_name => cachegrind.out.%p => cachegrind.out.%p
xdebug.remote_autostart => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_connect_back => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_enable => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_host => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_log => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_log_level => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_mode => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_port => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.remote_timeout => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.scream => Off => Off
xdebug.show_error_trace => Off => Off
xdebug.show_exception_trace => Off => Off
xdebug.show_local_vars => Off => Off
xdebug.show_mem_delta => (setting removed in Xdebug 3) => (setting removed in Xdebug 3)
xdebug.start_upon_error => default => default
xdebug.start_with_request => yes => yes
xdebug.trace_enable_trigger => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.trace_enable_trigger_value => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.trace_format => 0 => 0
xdebug.trace_options => 0 => 0
xdebug.trace_output_dir => (setting renamed in Xdebug 3) => (setting renamed in Xdebug 3)
xdebug.trace_output_name => trace.%c => trace.%c
xdebug.trigger_value => no value => no value
xdebug.use_compression => 1 => 1
xdebug.var_display_max_children => 128 => 128
xdebug.var_display_max_data => 512 => 512
xdebug.var_display_max_depth => 3 => 3

xml

XML Support => active
XML Namespace Support => active
libxml2 Version => 2.11.9

xmlreader

XMLReader => enabled

xmlwriter

XMLWriter => enabled

zlib

ZLib Support => enabled
Stream Wrapper => compress.zlib://
Stream Filter => zlib.inflate, zlib.deflate
Compiled Version => 1.3.1
Linked Version => 1.3.1

Directive => Local Value => Master Value
zlib.output_compression => Off => Off
zlib.output_compression_level => -1 => -1
zlib.output_handler => no value => no value

Additional Modules

Module Name

Environment

Variable => Value
ALLUSERSPROFILE => C:\ProgramData
ANDROID_HOME => C:\Users\<USER>\AppData\Local\Android\Sdk
APPDATA => C:\Users\<USER>\AppData\Roaming
CARGO_HOME => $persist_dir\.cargo
ChocolateyInstall => C:\ProgramData\chocolatey
ChocolateyLastPathUpdate => 133890938437440138
ChocolateyToolsLocation => C:\tools
CHROME_CRASHPAD_PIPE_NAME => \\.\pipe\crashpad_7860_HBUSTIHTXLTIJVHN
CommonProgramFiles => C:\Program Files\Common Files
CommonProgramFiles(x86) => C:\Program Files (x86)\Common Files
CommonProgramW6432 => C:\Program Files\Common Files
COMPOSER_HOME => C:\Users\<USER>\scoop\persist\composer\home
COMPUTERNAME => DESKTOP-HOME
ComSpec => C:\WINDOWS\system32\cmd.exe
CONDA_EXE => C:\Users\<USER>\anaconda3\Scripts\conda.exe
DriverData => C:\Windows\System32\Drivers\DriverData
EFC_2672_1262719628 => 1
EFC_2672_1592913036 => 1
EFC_2672_2283032206 => 1
EFC_2672_2775293581 => 1
EFC_2672_3789132940 => 1
FPS_BROWSER_APP_PROFILE_STRING => Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING => Default
GRADLE_USER_HOME => C:\Users\<USER>\scoop\apps\gradle\current\.gradle
HOMEDRIVE => C:
HOMEPATH => \Users\franz
JAVA_HOME => C:\Program Files\OpenJDK\jdk-22.0.2
LOCALAPPDATA => C:\Users\<USER>\AppData\Local
LOGONSERVER => \\DESKTOP-HOME
NUMBER_OF_PROCESSORS => 20
NVM_HOME => C:\ProgramData\nvm
NVM_SYMLINK => C:\Program Files\nodejs
OneDrive => C:\Users\<USER>\OneDrive
OneDriveConsumer => C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP => undefined
OS => Windows_NT
Path => C:\Program Files\PowerShell\7;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\ProgramData\chocolatey\bin;C:\ProgramData\nvm;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\texlive\2020\bin\win32;C:\ProgramData\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin;C:\Program Files\dotnet\;C:\Program Files\Calibre2\;C:\Program Files\Git\cmd;C:\Program Files\OpenJDK\jdk-22.0.2\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\PowerShell\7\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\scoop\apps\composer\current\home\vendor\bin;C:\Users\<USER>\scoop\apps\nodejs\current\bin;C:\Users\<USER>\scoop\apps\nodejs\current;C:\Users\<USER>\go\bin;C:\Python312\Scripts\;C:\Python312\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\ProgramData\chocolatey\bin;C:\ProgramData\nvm;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\texlive\2020\bin\win32;C:\Program Files\nodejs;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin;C:\Program Files\RedHat\Podman\;C:\ProgramData\ComposerSetup\bin;C:\Program Files\dotnet\;C:\Program Files\Calibre2\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\PowerShell\7\;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\PowerShell\7;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\scoop\apps\rustup\current\.cargo\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\cmdermini;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;C:\Users\<USER>\AppData\Local\Pandoc;C:\tools\neovim\nvim-win64\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;\.cargo\bin;.;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\scoop\persist\nodejs\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT => .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PHP_INI_SCAN_DIR => C:\Users\<USER>\scoop\apps\php\current\cli;C:\Users\<USER>\scoop\apps\php\current\cli\conf.d;
PODMAN_CONNECTIONS_CONF => C:\Users\<USER>\scoop\apps\podman\current\connections\podman-connections.json
PODMAN_DESKTOP_HOME_DIR => C:\Users\<USER>\scoop\apps\podman-desktop\current\config
POWERSHELL_DISTRIBUTION_CHANNEL => MSI:Windows 10 Pro
PROCESSOR_ARCHITECTURE => AMD64
PROCESSOR_IDENTIFIER => Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
PROCESSOR_LEVEL => 6
PROCESSOR_REVISION => a505
ProgramData => C:\ProgramData
ProgramFiles => C:\Program Files
ProgramFiles(x86) => C:\Program Files (x86)
ProgramW6432 => C:\Program Files
PSModulePath => C:\Users\<USER>\OneDrive\Dokumente\PowerShell\Modules;C:\Program Files\PowerShell\Modules;c:\program files\powershell\7\Modules;C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\platform\PowerShell;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\platform\PowerShell
PUBLIC => C:\Users\<USER>\.rustup
SESSIONNAME => Console
SystemDrive => C:
SystemRoot => C:\WINDOWS
TEMP => C:\Users\<USER>\AppData\Local\Temp
TMP => C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN => DESKTOP-HOME
USERDOMAIN_ROAMINGPROFILE => DESKTOP-HOME
USERNAME => franz
USERPROFILE => C:\Users\<USER>\WINDOWS
WIRESHARK_CONFIG_DIR => C:\Users\<USER>\scoop\apps\wireshark\current\Data
WIRESHARK_DATA_DIR => C:\Users\<USER>\scoop\apps\wireshark\current\Data
_CE_CONDA =>  
_CE_M =>  
_CONDA_EXE => C:\Users\<USER>\anaconda3\Scripts\conda.exe
_CONDA_ROOT => C:\Users\<USER>\anaconda3
__COMPAT_LAYER => DetectorsAppHealth
GIT_PAGER =>  
TERM_PROGRAM => vscode
TERM_PROGRAM_VERSION => 1.103.2
LANG => en_US.UTF-8
COLORTERM => truecolor
GIT_ASKPASS => c:\Program Files\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
VSCODE_GIT_ASKPASS_NODE => C:\Program Files\Microsoft VS Code\Code.exe
VSCODE_GIT_ASKPASS_EXTRA_ARGS =>  
VSCODE_GIT_ASKPASS_MAIN => c:\Program Files\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_IPC_HANDLE => \\.\pipe\vscode-git-825d1dbecd-sock
VSCODE_INJECTION => 1

PHP Variables

Variable => Value
$_SERVER['ALLUSERSPROFILE'] => C:\ProgramData
$_SERVER['ANDROID_HOME'] => C:\Users\<USER>\AppData\Local\Android\Sdk
$_SERVER['APPDATA'] => C:\Users\<USER>\AppData\Roaming
$_SERVER['CARGO_HOME'] => $persist_dir\.cargo
$_SERVER['ChocolateyInstall'] => C:\ProgramData\chocolatey
$_SERVER['ChocolateyLastPathUpdate'] => 133890938437440138
$_SERVER['ChocolateyToolsLocation'] => C:\tools
$_SERVER['CHROME_CRASHPAD_PIPE_NAME'] => \\.\pipe\crashpad_7860_HBUSTIHTXLTIJVHN
$_SERVER['CommonProgramFiles'] => C:\Program Files\Common Files
$_SERVER['CommonProgramFiles(x86)'] => C:\Program Files (x86)\Common Files
$_SERVER['CommonProgramW6432'] => C:\Program Files\Common Files
$_SERVER['COMPOSER_HOME'] => C:\Users\<USER>\scoop\persist\composer\home
$_SERVER['COMPUTERNAME'] => DESKTOP-HOME
$_SERVER['ComSpec'] => C:\WINDOWS\system32\cmd.exe
$_SERVER['CONDA_EXE'] => C:\Users\<USER>\anaconda3\Scripts\conda.exe
$_SERVER['DriverData'] => C:\Windows\System32\Drivers\DriverData
$_SERVER['EFC_2672_1262719628'] => 1
$_SERVER['EFC_2672_1592913036'] => 1
$_SERVER['EFC_2672_2283032206'] => 1
$_SERVER['EFC_2672_2775293581'] => 1
$_SERVER['EFC_2672_3789132940'] => 1
$_SERVER['FPS_BROWSER_APP_PROFILE_STRING'] => Internet Explorer
$_SERVER['FPS_BROWSER_USER_PROFILE_STRING'] => Default
$_SERVER['GRADLE_USER_HOME'] => C:\Users\<USER>\scoop\apps\gradle\current\.gradle
$_SERVER['HOMEDRIVE'] => C:
$_SERVER['HOMEPATH'] => \Users\franz
$_SERVER['JAVA_HOME'] => C:\Program Files\OpenJDK\jdk-22.0.2
$_SERVER['LOCALAPPDATA'] => C:\Users\<USER>\AppData\Local
$_SERVER['LOGONSERVER'] => \\DESKTOP-HOME
$_SERVER['NUMBER_OF_PROCESSORS'] => 20
$_SERVER['NVM_HOME'] => C:\ProgramData\nvm
$_SERVER['NVM_SYMLINK'] => C:\Program Files\nodejs
$_SERVER['OneDrive'] => C:\Users\<USER>\OneDrive
$_SERVER['OneDriveConsumer'] => C:\Users\<USER>\OneDrive
$_SERVER['ORIGINAL_XDG_CURRENT_DESKTOP'] => undefined
$_SERVER['OS'] => Windows_NT
$_SERVER['Path'] => C:\Program Files\PowerShell\7;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\ProgramData\chocolatey\bin;C:\ProgramData\nvm;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\texlive\2020\bin\win32;C:\ProgramData\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin;C:\Program Files\dotnet\;C:\Program Files\Calibre2\;C:\Program Files\Git\cmd;C:\Program Files\OpenJDK\jdk-22.0.2\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\PowerShell\7\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\scoop\apps\composer\current\home\vendor\bin;C:\Users\<USER>\scoop\apps\nodejs\current\bin;C:\Users\<USER>\scoop\apps\nodejs\current;C:\Users\<USER>\go\bin;C:\Python312\Scripts\;C:\Python312\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\ProgramData\chocolatey\bin;C:\ProgramData\nvm;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\texlive\2020\bin\win32;C:\Program Files\nodejs;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin;C:\Program Files\RedHat\Podman\;C:\ProgramData\ComposerSetup\bin;C:\Program Files\dotnet\;C:\Program Files\Calibre2\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\PowerShell\7\;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\PowerShell\7;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\scoop\apps\rustup\current\.cargo\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\cmdermini;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;C:\Users\<USER>\AppData\Local\Pandoc;C:\tools\neovim\nvim-win64\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;\.cargo\bin;.;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\scoop\persist\nodejs\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
$_SERVER['PATHEXT'] => .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
$_SERVER['PHP_INI_SCAN_DIR'] => C:\Users\<USER>\scoop\apps\php\current\cli;C:\Users\<USER>\scoop\apps\php\current\cli\conf.d;
$_SERVER['PODMAN_CONNECTIONS_CONF'] => C:\Users\<USER>\scoop\apps\podman\current\connections\podman-connections.json
$_SERVER['PODMAN_DESKTOP_HOME_DIR'] => C:\Users\<USER>\scoop\apps\podman-desktop\current\config
$_SERVER['POWERSHELL_DISTRIBUTION_CHANNEL'] => MSI:Windows 10 Pro
$_SERVER['PROCESSOR_ARCHITECTURE'] => AMD64
$_SERVER['PROCESSOR_IDENTIFIER'] => Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
$_SERVER['PROCESSOR_LEVEL'] => 6
$_SERVER['PROCESSOR_REVISION'] => a505
$_SERVER['ProgramData'] => C:\ProgramData
$_SERVER['ProgramFiles'] => C:\Program Files
$_SERVER['ProgramFiles(x86)'] => C:\Program Files (x86)
$_SERVER['ProgramW6432'] => C:\Program Files
$_SERVER['PSModulePath'] => C:\Users\<USER>\OneDrive\Dokumente\PowerShell\Modules;C:\Program Files\PowerShell\Modules;c:\program files\powershell\7\Modules;C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\platform\PowerShell;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules;C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\platform\PowerShell
$_SERVER['PUBLIC'] => C:\Users\<USER>\.rustup
$_SERVER['SESSIONNAME'] => Console
$_SERVER['SystemDrive'] => C:
$_SERVER['SystemRoot'] => C:\WINDOWS
$_SERVER['TEMP'] => C:\Users\<USER>\AppData\Local\Temp
$_SERVER['TMP'] => C:\Users\<USER>\AppData\Local\Temp
$_SERVER['USERDOMAIN'] => DESKTOP-HOME
$_SERVER['USERDOMAIN_ROAMINGPROFILE'] => DESKTOP-HOME
$_SERVER['USERNAME'] => franz
$_SERVER['USERPROFILE'] => C:\Users\<USER>\WINDOWS
$_SERVER['WIRESHARK_CONFIG_DIR'] => C:\Users\<USER>\scoop\apps\wireshark\current\Data
$_SERVER['WIRESHARK_DATA_DIR'] => C:\Users\<USER>\scoop\apps\wireshark\current\Data
$_SERVER['_CE_CONDA'] => 
$_SERVER['_CE_M'] => 
$_SERVER['_CONDA_EXE'] => C:\Users\<USER>\anaconda3\Scripts\conda.exe
$_SERVER['_CONDA_ROOT'] => C:\Users\<USER>\anaconda3
$_SERVER['__COMPAT_LAYER'] => DetectorsAppHealth
$_SERVER['GIT_PAGER'] => 
$_SERVER['TERM_PROGRAM'] => vscode
$_SERVER['TERM_PROGRAM_VERSION'] => 1.103.2
$_SERVER['LANG'] => en_US.UTF-8
$_SERVER['COLORTERM'] => truecolor
$_SERVER['GIT_ASKPASS'] => c:\Program Files\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
$_SERVER['VSCODE_GIT_ASKPASS_NODE'] => C:\Program Files\Microsoft VS Code\Code.exe
$_SERVER['VSCODE_GIT_ASKPASS_EXTRA_ARGS'] => 
$_SERVER['VSCODE_GIT_ASKPASS_MAIN'] => c:\Program Files\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
$_SERVER['VSCODE_GIT_IPC_HANDLE'] => \\.\pipe\vscode-git-825d1dbecd-sock
$_SERVER['VSCODE_INJECTION'] => 1
$_SERVER['PHP_SELF'] => 
$_SERVER['SCRIPT_NAME'] => 
$_SERVER['SCRIPT_FILENAME'] => 
$_SERVER['PATH_TRANSLATED'] => 
$_SERVER['DOCUMENT_ROOT'] => 
$_SERVER['REQUEST_TIME_FLOAT'] => **********.3605
$_SERVER['REQUEST_TIME'] => **********
$_SERVER['argv'] => Array
(
)

$_SERVER['argc'] => 0

PHP License
This program is free software; you can redistribute it and/or modify
it under the terms of the PHP License as published by the PHP Group
and included in the distribution in the file:  LICENSE

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

If you did not receive a copy of the PHP license, or have any
questions about PHP licensing, <NAME_EMAIL>.
